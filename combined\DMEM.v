module DMEM (
    input logic clk,
    input logic rst_n,
    input logic MemRead,
    input logic MemWrite,
    input logic [31:0] addr,
    input logic [31:0] WriteData,
    output logic [31:0] ReadData
);
    logic [31:0] memory [0:255];

    // Read operation
    assign ReadData = (MemRead) ? memory[addr[9:2]] : 32'b0;

    // Write operation with reset
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // Reset all memory locations to 0
            for (int i = 0; i < 256; i = i + 1)
                memory[i] <= 32'b0;
        end else if (MemWrite) begin
            // Write data to memory (word-aligned)
            memory[addr[9:2]] <= WriteData;
        end
    end
endmodule
