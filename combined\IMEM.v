module IMEM (
    input  [31:0] addr,
    output [31:0] Instruction
);
    reg [31:0] memory [0:1023];

    // Output instruction based on word-aligned address
    assign Instruction = memory[addr[31:2]]; // Use full address range like sc1

    initial begin
        // Initialize memory to NOPs
        for (int i = 0; i < 1024; i = i + 1) begin
            memory[i] = 32'h00000013; // NOP instruction
        end

        // Try to load from both possible memory files
        // This allows compatibility with both test cases
        if ($fopen("./mem/imem1.hex", "r") != 0) begin
            $readmemh("./mem/imem1.hex", memory);
            $display("IMEM: Loaded imem1.hex");
        end else if ($fopen("./mem/imem2.hex", "r") != 0) begin
            $readmemh("./mem/imem2.hex", memory);
            $display("IMEM: Loaded imem2.hex");
        end else begin
            $display("IMEM: No memory file found, using NOPs");
        end

        // Display first few instructions for debugging
        $display("IMEM[0]: %h", memory[0]);
        $display("IMEM[1]: %h", memory[1]);
        $display("IMEM[2]: %h", memory[2]);
    end
endmodule
