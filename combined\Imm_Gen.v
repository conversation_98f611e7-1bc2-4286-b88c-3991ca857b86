module Imm_Gen (
    input  logic [31:0] inst,
    output logic [31:0] imm_out
);
    // Extract opcode outside of always block to avoid warnings
    logic [6:0] opcode;
    assign opcode = inst[6:0];

    always_comb begin
        case (opcode)
            7'b0000011, // I-type (Load)
            7'b0010011, // I-type (ALU immediate)
            7'b1100111: // I-type (JALR)
                imm_out = {{20{inst[31]}}, inst[31:20]};

            7'b0100011: // S-type (Store)
                imm_out = {{20{inst[31]}}, inst[31:25], inst[11:7]};

            7'b1100011: // B-type (Branch)
                imm_out = {{19{inst[31]}}, inst[31], inst[7], inst[30:25], inst[11:8], 1'b0};

            7'b0010111, // U-type (AUIPC)
            7'b0110111: // U-type (LUI)
                imm_out = {inst[31:12], 12'b0};

            7'b1101111: // J-type (JAL)
                imm_out = {{11{inst[31]}}, inst[31], inst[19:12], inst[20], inst[30:21], 1'b0};

            default:
                imm_out = 32'b0;
        endcase
    end
endmodule
