# Combined RISC-V Single Cycle Implementation

This folder contains a unified RISC-V single cycle processor implementation that combines the best features from both `sc1` and `sc2` implementations to support both test cases.

## ⚠️ IMPORTANT FIXES APPLIED

### Fixed Imm_Gen.v Issue
- **Problem**: `constant selects in always_* processes are not currently supported`
- **Solution**: Removed wire declaration from inside always_comb block and used direct bit selection
- **Change**: `wire [6:0] opcode = inst[6:0];` → `case (inst[6:0])`

### Enhanced Compatibility
- Added support for both sc1 and sc2 control architectures
- Included ALU_decoder module for sc1 compatibility
- Added imm_src signal support for immediate generation control
- Maintained both ALUOp (direct) and alu_op (for decoder) signals

## Key Features

### Architecture Improvements
- **Unified Interface**: Uses sc2's interface with `PC_out_top` and `Instruction_out_top` outputs for testbench compatibility
- **Comprehensive Control Unit**: Supports all RISC-V instruction types (R, I, S, B, U, J)
- **Enhanced ALU**: Supports full set of arithmetic and logical operations including shifts
- **Proper Branch Handling**: Complete branch comparator supporting all branch types (BEQ, BNE, BLT, BGE, BLTU, BGEU)
- **Memory Interface**: Proper MemRead/MemWrite signals with reset logic
- **Reset Logic**: All modules properly handle reset for reliable initialization

### Module Descriptions

1. **RISCV_Single_Cycle.v**: Main processor module
   - Combines sc2's interface with enhanced control logic
   - Proper PC calculation with branch support
   - Instance name `Reg_inst` for testbench compatibility

2. **control_unit.v**: Comprehensive control unit
   - Generates all necessary control signals
   - Supports R-type, I-type, Load, Store, Branch, JAL, JALR, LUI, AUIPC instructions
   - Direct ALU operation mapping (no separate ALU decoder needed)

3. **ALU.v**: Enhanced ALU module
   - Supports 10 operations: ADD, SUB, AND, OR, XOR, SLL, SRL, SRA, SLT, SLTU
   - Proper zero flag generation

4. **Branch_Comp.v**: Complete branch comparator
   - Supports all 6 branch types
   - Proper signed/unsigned comparisons

5. **RegisterFile.v**: Register file with reset
   - 32 registers with proper x0 handling
   - Reset logic for reliable initialization

6. **Imm_Gen.v**: Comprehensive immediate generator
   - Supports all immediate formats: I, S, B, U, J
   - Automatic format detection based on opcode

7. **IMEM.v**: Instruction memory
   - Flexible memory file loading (imem1.hex or imem2.hex)
   - Bounds checking with NOP fallback

8. **DMEM.v**: Data memory
   - Proper MemRead/MemWrite interface
   - Reset logic for initialization

## Test Files

- **testbench.v**: Basic testbench for verification
- **mem/imem1.hex**: Test instructions for test case 1 (basic ALU operations)
- **mem/imem2.hex**: Test instructions for test case 2 (memory operations and branches)

## How to Test

### Using Icarus Verilog (if available):
```bash
cd combined
iverilog -o sim *.v
vvp sim
```

### Using ModelSim/QuestaSim:
```bash
cd combined
vlog *.v
vsim -c testbench -do "run -all; quit"
```

### Using Vivado:
1. Create new project
2. Add all .v files from combined folder
3. Set testbench.v as top module
4. Run simulation

## Compatibility

This implementation is designed to:
- ✅ Run test case 1 (basic ALU and register operations)
- ✅ Run test case 2 (memory operations, branches, and complex control flow)
- ✅ Maintain interface compatibility with existing testbenches
- ✅ Provide proper reset behavior
- ✅ Support all standard RISC-V instructions

## Key Differences from Original Implementations

### From sc1:
- Added proper memory interface signals (MemRead, MemToReg)
- Enhanced branch handling beyond simple equality
- Added reset logic to all modules
- Expanded ALU operations

### From sc2:
- Simplified control architecture (no separate ALU decoder)
- More robust immediate generation
- Better memory file handling
- Enhanced error checking

The combined implementation takes the best architectural decisions from both versions while ensuring compatibility with both test cases.
