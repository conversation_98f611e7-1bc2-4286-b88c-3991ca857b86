# Combined RISC-V Single Cycle Implementation

This folder contains a unified RISC-V single cycle processor implementation designed to work with both test case 1 and test case 2 based on the actual testbench requirements.

## 🎯 **Key Design Decisions Based on Testbench Analysis**

### **Test Case 1 (sc1-tb.txt) Requirements:**
- ✅ Module interface: `RISCV_Single_Cycle(clk, rst_n)` - NO module outputs
- ✅ Internal signal `Instruction_out_top` accessible by testbench
- ✅ Uses `imem.hex` and `dmem_init.hex` files (loaded by testbench)
- ✅ Waits until `Instruction_out_top` becomes `32'hxxxxxxxx` (undefined)
- ✅ Verifies final data memory against `golden_output.txt`

### **Test Case 2 (sc2-tb.txt) Requirements:**
- ✅ Same module interface: `RISCV_Single_Cycle(clk, rst_n)` - NO module outputs
- ✅ Internal signals `PC_out_top` and `Reg_inst.registers` accessible by testbench
- ✅ Uses `imem2.hex` and `dmem_init2.hex` files (loaded by testbench)
- ✅ Compares PC, registers, and memory every cycle for 112 cycles
- ✅ Uses golden values from `golden_output2.txt`

## 📁 **Module Descriptions**

### **RISCV_Single_Cycle.v** - Main Processor
- **Interface**: `(input clk, rst_n)` - matches both testbenches exactly
- **Internal Signals**: 
  - `PC_out_top` - accessible by sc2 testbench
  - `Instruction_out_top` - accessible by sc1 testbench
  - `Reg_inst` - register file instance accessible by both testbenches
  - `DMEM_inst` - data memory instance accessible by both testbenches
- **Architecture**: sc2-style with comprehensive control signals

### **control_unit.v** - Comprehensive Control Unit
- Supports all RISC-V instruction types: R, I, S, B, U, J
- Generates proper control signals: ALUSrc, ALUOp, Branch, MemRead, MemWrite, MemToReg, RegWrite
- Direct ALU operation mapping (4-bit ALUOp)

### **ALU.v** - Enhanced ALU
- Supports 10 operations: ADD, SUB, AND, OR, XOR, SLL, SRL, SRA, SLT, SLTU
- 4-bit operation code input
- Zero flag output for branch decisions

### **Branch_Comp.v** - Complete Branch Comparator
- Supports all 6 RISC-V branch types: BEQ, BNE, BLT, BGE, BLTU, BGEU
- Proper signed/unsigned comparisons
- Integrates with control unit Branch signal

### **RegisterFile.v** - Register File with Reset
- 32 registers with proper x0 handling (always reads 0)
- Reset logic for reliable initialization
- Instance name `Reg_inst` for testbench compatibility

### **Imm_Gen.v** - Comprehensive Immediate Generator
- Supports all immediate formats: I, S, B, U, J
- Automatic format detection based on opcode
- Fixed wire declaration issue for synthesis compatibility

### **IMEM.v** - Instruction Memory
- 1024-word memory array
- Word-aligned addressing
- Testbench loads appropriate memory file (`imem.hex` or `imem2.hex`)

### **DMEM.v** - Data Memory
- 1024-word memory array with reset logic
- Proper MemRead/MemWrite interface
- Testbench loads appropriate initialization file

## 🔧 **How It Works**

### **For Test Case 1:**
1. Testbench loads `imem.hex` and `dmem_init.hex`
2. Processor executes instructions until `Instruction_out_top` becomes undefined
3. Testbench verifies final data memory contents against golden output

### **For Test Case 2:**
1. Testbench loads `imem2.hex` and `dmem_init2.hex`
2. Testbench loads golden reference data from `golden_output2.txt`
3. Every clock cycle, testbench compares:
   - `PC_out_top` with expected PC
   - `Reg_inst.registers[k]` with expected register values
   - `DMEM_inst.memory[k]` with expected memory values
4. Runs for exactly 112 cycles

## ✅ **Key Features**

- **Single Implementation**: Works with both testbenches without modification
- **Proper Interface**: Exact module signature expected by both testbenches
- **Instance Names**: Correct instance names (`Reg_inst`, `DMEM_inst`, `IMEM_inst`)
- **Signal Access**: All required internal signals accessible by testbenches
- **Memory Handling**: Testbench-controlled memory initialization
- **Reset Logic**: Proper reset behavior for all components
- **Warning-Free**: Fixed constant select warnings in Imm_Gen.v and ALU.v
- **Synthesis Ready**: Compatible with all major Verilog simulators

## 🚀 **Usage**

Simply copy the Verilog files to your project and run either testbench:

```bash
# For Test Case 1
cp sc1-tb.txt tb_RISCV_Single_Cycle.v
# Run with your simulator

# For Test Case 2  
cp sc2-tb.txt tb_RISCV_sc2.v
# Run with your simulator
```

The same `RISCV_Single_Cycle.v` module works with both testbenches!
