`timescale 1ns/1ps

// Simple test to verify the combined implementation compiles and runs
module test_combined;
    logic clk;
    logic rst_n;

    // Instantiate the processor
    RISCV_Single_Cycle dut (
        .clk(clk),
        .rst_n(rst_n)
    );

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk; // 10ns period
    end

    // Test sequence
    initial begin
        $display("=== Combined RISC-V Implementation Test ===");
        
        // Initialize some test instructions in memory
        dut.IMEM_inst.memory[0] = 32'h00500093; // ADDI x1, x0, 5
        dut.IMEM_inst.memory[1] = 32'h00300113; // ADDI x2, x0, 3  
        dut.IMEM_inst.memory[2] = 32'h002081b3; // ADD x3, x1, x2
        dut.IMEM_inst.memory[3] = 32'h00000013; // NOP
        
        // Reset sequence
        rst_n = 0;
        #20;
        rst_n = 1;
        #10;
        
        $display("Starting execution...");
        
        // Run for a few cycles
        repeat(10) begin
            @(posedge clk);
            $display("Cycle: PC=%h, Inst=%h, RegWrite=%b", 
                     dut.PC_out_top, dut.Instruction_out_top, dut.RegWrite);
            
            if (dut.RegWrite && dut.rd != 0) begin
                $display("  -> Writing %h to register x%0d", dut.WriteData, dut.rd);
            end
        end
        
        // Check register file
        $display("\nFinal Register Contents:");
        for (int i = 0; i < 8; i++) begin
            if (dut.Reg_inst.registers[i] != 0) begin
                $display("x%0d = %h", i, dut.Reg_inst.registers[i]);
            end
        end
        
        // Verify expected results
        if (dut.Reg_inst.registers[1] == 32'h5 && 
            dut.Reg_inst.registers[2] == 32'h3 && 
            dut.Reg_inst.registers[3] == 32'h8) begin
            $display("✅ Test PASSED: Basic ALU operations work correctly");
        end else begin
            $display("❌ Test FAILED: Unexpected register values");
        end
        
        $display("=== Test Complete ===");
        $finish;
    end

endmodule
