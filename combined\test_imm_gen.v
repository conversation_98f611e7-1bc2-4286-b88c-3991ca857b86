`timescale 1ns/1ps

module test_imm_gen;
    logic [31:0] inst;
    logic [31:0] imm_out;
    
    Imm_Gen dut (
        .inst(inst),
        .imm_out(imm_out)
    );
    
    initial begin
        $display("Testing Imm_Gen module");
        
        // Test I-type instruction: ADDI x1, x0, 5 (00500093)
        inst = 32'h00500093;
        #1;
        $display("I-type (ADDI): inst=%h, imm_out=%h (expected: 00000005)", inst, imm_out);
        
        // Test R-type instruction: ADD x3, x1, x2 (002081b3)
        inst = 32'h002081b3;
        #1;
        $display("R-type (ADD): inst=%h, imm_out=%h (expected: 00000000)", inst, imm_out);
        
        $display("Imm_Gen test completed");
        $finish;
    end
endmodule
