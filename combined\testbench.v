`timescale 1ns/1ps

module testbench;
    logic clk;
    logic rst_n;
    logic [31:0] PC_out_top;
    logic [31:0] Instruction_out_top;

    // Instantiate the RISC-V processor
    RISCV_Single_Cycle dut (
        .clk(clk),
        .rst_n(rst_n),
        .PC_out_top(PC_out_top),
        .Instruction_out_top(Instruction_out_top)
    );

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk; // 10ns period (100MHz)
    end

    // Test sequence
    initial begin
        $display("Starting RISC-V Single Cycle Test");
        
        // Reset sequence
        rst_n = 0;
        #20;
        rst_n = 1;
        #10;
        
        // Run for several clock cycles to see basic operation
        repeat(20) begin
            @(posedge clk);
            $display("Time: %0t, PC: %h, Instruction: %h", $time, PC_out_top, Instruction_out_top);
        end
        
        // Check register file state
        $display("\nRegister File Contents:");
        for (int i = 0; i < 8; i++) begin
            $display("x%0d: %h", i, dut.Reg_inst.registers[i]);
        end
        
        $display("\nTest completed successfully!");
        $finish;
    end

    // Monitor important signals
    initial begin
        $monitor("Time: %0t | PC: %h | Inst: %h | RegWrite: %b | MemWrite: %b | Branch: %b", 
                 $time, PC_out_top, Instruction_out_top, 
                 dut.RegWrite, dut.MemWrite, dut.Branch);
    end

endmodule
