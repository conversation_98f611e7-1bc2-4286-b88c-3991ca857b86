// Basic test instructions for test case 2
// ADDI x1, x0, 10   (x1 = 10)
00a00093
// ADDI x2, x0, 20   (x2 = 20)
01400113
// SW x2, 0(x1)      (store x2 to memory[x1])
00212023
// LW x3, 0(x1)      (load from memory[x1] to x3)
0000a183
// BEQ x2, x3, 8     (branch if x2 == x3, skip next instruction)
00310463
// ADDI x4, x0, 99   (should be skipped)
06300213
// ADDI x5, x0, 42   (x5 = 42)
02a00293
// NOP (infinite loop to stop)
00000013
00000013
